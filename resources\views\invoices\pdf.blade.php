<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hóa đơn #{{ $invoice->invoice_number }}</title>
    <style>
        body {
            font-family: 'DejaVu Sans', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 5px;
        }
        .company-info {
            font-size: 11px;
            color: #666;
        }
        .invoice-title {
            font-size: 20px;
            font-weight: bold;
            text-align: center;
            margin: 30px 0;
            color: #1f2937;
        }
        .invoice-info {
            display: table;
            width: 100%;
            margin-bottom: 30px;
        }
        .invoice-info-left, .invoice-info-right {
            display: table-cell;
            width: 50%;
            vertical-align: top;
        }
        .invoice-info-right {
            text-align: right;
        }
        .customer-info {
            background-color: #f8fafc;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        .customer-info h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #374151;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        .items-table th {
            background-color: #374151;
            color: white;
            padding: 12px 8px;
            text-align: left;
            font-weight: bold;
        }
        .items-table td {
            padding: 10px 8px;
            border-bottom: 1px solid #e5e7eb;
        }
        .items-table tr:nth-child(even) {
            background-color: #f9fafb;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .summary {
            float: right;
            width: 300px;
            margin-top: 20px;
        }
        .summary-row {
            display: table;
            width: 100%;
            margin-bottom: 8px;
        }
        .summary-label, .summary-value {
            display: table-cell;
            padding: 5px 0;
        }
        .summary-label {
            text-align: left;
        }
        .summary-value {
            text-align: right;
            font-weight: bold;
        }
        .total-row {
            border-top: 2px solid #374151;
            font-size: 16px;
            font-weight: bold;
            color: #1f2937;
        }
        .footer {
            clear: both;
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            text-align: center;
            font-size: 11px;
            color: #6b7280;
        }
        .payment-info {
            margin-top: 30px;
            background-color: #f0f9ff;
            padding: 15px;
            border-radius: 5px;
        }
        .payment-info h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #374151;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-confirmed {
            background-color: #dcfce7;
            color: #166534;
        }
        .status-draft {
            background-color: #fef3c7;
            color: #92400e;
        }
        .status-paid {
            background-color: #dcfce7;
            color: #166534;
        }
        .status-pending {
            background-color: #fef3c7;
            color: #92400e;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="company-name">{{ tenant()->name ?? 'Nhà thuốc ABC' }}</div>
        <div class="company-info">
            Địa chỉ: {{ tenant()->address ?? '123 Đường ABC, Quận 1, TP.HCM' }}<br>
            Điện thoại: {{ tenant()->phone ?? '0123 456 789' }} | Email: {{ tenant()->email ?? '<EMAIL>' }}<br>
            Mã số thuế: {{ tenant()->tax_code ?? '0123456789' }}
        </div>
    </div>

    <!-- Invoice Title -->
    <div class="invoice-title">HÓA ĐƠN BÁN HÀNG</div>

    <!-- Invoice Info -->
    <div class="invoice-info">
        <div class="invoice-info-left">
            <strong>Số hóa đơn:</strong> {{ $invoice->invoice_number }}<br>
            <strong>Ngày xuất:</strong> {{ $invoice->invoice_date->format('d/m/Y H:i') }}<br>
            <strong>Thu ngân:</strong> {{ $invoice->user->name }}
        </div>
        <div class="invoice-info-right">
            <strong>Trạng thái:</strong> 
            <span class="status-badge status-{{ $invoice->status }}">
                @switch($invoice->status)
                    @case('confirmed') Đã xác nhận @break
                    @case('draft') Nháp @break
                    @case('cancelled') Đã hủy @break
                    @default {{ $invoice->status }}
                @endswitch
            </span><br>
            <strong>Thanh toán:</strong>
            <span class="status-badge status-{{ $invoice->payment_status }}">
                @switch($invoice->payment_status)
                    @case('paid') Đã thanh toán @break
                    @case('pending') Chờ thanh toán @break
                    @case('partial') Thanh toán 1 phần @break
                    @default {{ $invoice->payment_status }}
                @endswitch
            </span>
        </div>
    </div>

    <!-- Customer Info -->
    @if($invoice->customer)
    <div class="customer-info">
        <h3>Thông tin khách hàng</h3>
        <strong>Tên:</strong> {{ $invoice->customer->name }}<br>
        @if($invoice->customer->phone)
            <strong>Điện thoại:</strong> {{ $invoice->customer->phone }}<br>
        @endif
        @if($invoice->customer->email)
            <strong>Email:</strong> {{ $invoice->customer->email }}<br>
        @endif
        @if($invoice->customer->address)
            <strong>Địa chỉ:</strong> {{ $invoice->customer->address }}
        @endif
    </div>
    @endif

    <!-- Items Table -->
    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 5%">STT</th>
                <th style="width: 35%">Tên thuốc</th>
                <th style="width: 15%">Số lô</th>
                <th style="width: 10%">SL</th>
                <th style="width: 15%">Đơn giá</th>
                <th style="width: 10%">Thuế</th>
                <th style="width: 15%">Thành tiền</th>
            </tr>
        </thead>
        <tbody>
            @foreach($invoice->items as $index => $item)
            <tr>
                <td class="text-center">{{ $index + 1 }}</td>
                <td>
                    <strong>{{ $item->batch->drug->name }}</strong><br>
                    <small>{{ $item->batch->drug->concentration ?? '' }}</small>
                </td>
                <td class="text-center">{{ $item->batch->batch_number }}</td>
                <td class="text-center">{{ $item->quantity }}</td>
                <td class="text-right">{{ number_format($item->unit_price) }}₫</td>
                <td class="text-center">{{ number_format($item->tax_rate, 1) }}%</td>
                <td class="text-right"><strong>{{ number_format($item->total_price) }}₫</strong></td>
            </tr>
            @endforeach
        </tbody>
    </table>

    <!-- Summary -->
    <div class="summary">
        <div class="summary-row">
            <div class="summary-label">Tổng tiền hàng:</div>
            <div class="summary-value">{{ number_format($invoice->subtotal) }}₫</div>
        </div>
        @if($invoice->discount_amount > 0)
        <div class="summary-row">
            <div class="summary-label">Giảm giá:</div>
            <div class="summary-value" style="color: #dc2626;">-{{ number_format($invoice->discount_amount) }}₫</div>
        </div>
        @endif
        <div class="summary-row">
            <div class="summary-label">Thuế GTGT:</div>
            <div class="summary-value">{{ number_format($invoice->tax_amount) }}₫</div>
        </div>
        <div class="summary-row total-row">
            <div class="summary-label">Tổng thanh toán:</div>
            <div class="summary-value">{{ number_format($invoice->total_amount) }}₫</div>
        </div>
    </div>

    <!-- Payment Info -->
    <div class="payment-info">
        <h3>Thông tin thanh toán</h3>
        <strong>Phương thức:</strong> 
        @switch($invoice->payment_method)
            @case('cash') Tiền mặt @break
            @case('card') Thẻ @break
            @case('transfer') Chuyển khoản @break
            @case('qr_code') QR Code @break
            @default {{ $invoice->payment_method }}
        @endswitch
        @if($invoice->notes)
            <br><strong>Ghi chú:</strong> {{ $invoice->notes }}
        @endif
    </div>

    <!-- Footer -->
    <div class="footer">
        <p>Cảm ơn quý khách đã sử dụng dịch vụ của chúng tôi!</p>
        <p>Hóa đơn được tạo tự động bởi hệ thống quản lý nhà thuốc</p>
        <p>In lúc: {{ now()->format('d/m/Y H:i:s') }}</p>
    </div>
</body>
</html>
