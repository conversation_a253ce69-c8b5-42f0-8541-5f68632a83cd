<?php

return [
    /*
    |--------------------------------------------------------------------------
    | SMS Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for SMS service integration
    |
    */

    'default' => env('SMS_DRIVER', 'mock'),

    'drivers' => [
        'mock' => [
            'class' => \App\Services\SmsService::class,
        ],
        
        'esms' => [
            'class' => \App\Services\EsmsSmsService::class,
            'api_key' => env('ESMS_API_KEY'),
            'secret_key' => env('ESMS_SECRET_KEY'),
            'brand_name' => env('ESMS_BRAND_NAME', 'PharmaSaaS'),
        ],
        
        'vietguys' => [
            'class' => \App\Services\VietguysSmsService::class,
            'username' => env('VIETGUYS_USERNAME'),
            'password' => env('VIETGUYS_PASSWORD'),
            'brand_name' => env('VIETGUYS_BRAND_NAME', 'PharmaSaaS'),
        ],
    ],

    // General SMS settings
    'api_url' => env('SMS_API_URL', 'https://api.sms-provider.com'),
    'api_key' => env('SMS_API_KEY', ''),
    'brand_name' => env('SMS_BRAND_NAME', 'PharmaSaaS'),
    'mock' => env('SMS_MOCK', true),

    // Rate limiting
    'rate_limit' => [
        'max_per_minute' => env('SMS_RATE_LIMIT_PER_MINUTE', 60),
        'max_per_hour' => env('SMS_RATE_LIMIT_PER_HOUR', 1000),
        'max_per_day' => env('SMS_RATE_LIMIT_PER_DAY', 10000),
    ],

    // Message templates
    'templates' => [
        'invoice_ready' => 'Hóa đơn #{invoice_number} đã sẵn sàng. Xem tại: {portal_url}',
        'payment_reminder' => 'Nhắc nhở thanh toán: Hóa đơn #{invoice_number} với số tiền {amount} chưa được thanh toán.',
        'promotion' => '🎉 Khuyến mãi từ {brand_name}: {promotion_text}',
        'appointment_reminder' => 'Nhắc nhở: Bạn có lịch hẹn vào {appointment_time} tại {pharmacy_name}',
        'prescription_ready' => 'Đơn thuốc #{prescription_number} đã sẵn sàng để nhận tại {pharmacy_name}',
    ],

    // Blacklist phone numbers (for testing)
    'blacklist' => [
        // '84901234567',
    ],

    // Whitelist phone numbers (for testing - only these numbers will receive SMS in test mode)
    'whitelist' => [
        // '84901234567',
    ],

    // Logging
    'log_channel' => env('SMS_LOG_CHANNEL', 'daily'),
    'log_failed_attempts' => env('SMS_LOG_FAILED_ATTEMPTS', true),
];
