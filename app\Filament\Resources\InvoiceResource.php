<?php

namespace App\Filament\Resources;

use App\Filament\Resources\InvoiceResource\Pages;
use App\Filament\Resources\InvoiceResource\RelationManagers;
use App\Filament\Resources\InvoiceResource\Widgets;
use App\Models\Invoice;
use App\Models\Customer;
use App\Models\Batch;
use App\Services\InvoiceService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Get;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\DatePicker;
use Filament\Support\Enums\FontWeight;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Closure;

class InvoiceResource extends Resource
{
    protected static ?string $model = Invoice::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $navigationGroup = 'Sales';
    protected static ?string $navigationLabel = 'Hóa đơn';
    protected static ?string $modelLabel = 'Hóa đơn';
    protected static ?string $pluralModelLabel = 'Hóa đơn';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Thông tin hóa đơn')
                    ->schema([
                        Grid::make(3)->schema([
                            TextInput::make('invoice_number')
                                ->label('Số hóa đơn')
                                ->default(fn () => app(InvoiceService::class)->generateInvoiceNumber())
                                ->disabled()
                                ->dehydrated(),

                            Select::make('customer_id')
                                ->label('Khách hàng')
                                ->relationship('customer', 'name')
                                ->searchable()
                                ->preload()
                                ->createOptionForm([
                                    TextInput::make('name')->label('Tên khách hàng')->required(),
                                    TextInput::make('phone')->label('Số điện thoại'),
                                    TextInput::make('email')->label('Email'),
                                ])
                                ->reactive()
                                ->afterStateUpdated(function ($state, callable $set) {
                                    if ($state) {
                                        $customer = Customer::find($state);
                                        $set('customer_discount_rate', $customer->discount_rate ?? 0);
                                    }
                                }),

                            DateTimePicker::make('invoice_date')
                                ->label('Ngày xuất hóa đơn')
                                ->default(now())
                                ->required(),
                        ]),

                        Grid::make(2)->schema([
                            Select::make('payment_method')
                                ->label('Phương thức thanh toán')
                                ->options([
                                    'cash' => 'Tiền mặt',
                                    'card' => 'Thẻ',
                                    'transfer' => 'Chuyển khoản',
                                    'qr_code' => 'QR Code',
                                ])
                                ->default('cash')
                                ->required(),

                            Select::make('payment_status')
                                ->label('Trạng thái thanh toán')
                                ->options([
                                    'pending' => 'Chờ thanh toán',
                                    'paid' => 'Đã thanh toán',
                                    'partial' => 'Thanh toán một phần',
                                ])
                                ->default('paid')
                                ->required(),
                        ]),
                    ]),

                Section::make('Chi tiết hóa đơn')
                    ->schema([
                        Repeater::make('items')
                            ->relationship()
                            ->schema([
                                Select::make('batch_id')
                                    ->label('Lô thuốc')
                                    ->relationship('batch', 'batch_number')
                                    ->getOptionLabelFromRecordUsing(fn (Batch $record) =>
                                        "{$record->drug->name} - Lô: {$record->batch_number} (Tồn: {$record->current_quantity}) - " . number_format($record->selling_price) . "₫"
                                    )
                                    ->searchable(['batch_number'])
                                    ->preload()
                                    ->required()
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set) {
                                        if ($state) {
                                            $batch = Batch::with('drug')->find($state);
                                            $set('unit_price', $batch->selling_price);
                                            $set('max_quantity', $batch->current_quantity);
                                            $set('drug_name', $batch->drug->name);
                                            $set('tax_rate', $batch->drug->tax_rate * 100);
                                        }
                                    }),

                                TextInput::make('drug_name')
                                    ->label('Tên thuốc')
                                    ->disabled()
                                    ->dehydrated(false),

                                TextInput::make('quantity')
                                    ->label('Số lượng')
                                    ->numeric()
                                    ->required()
                                    ->minValue(1)
                                    ->reactive()
                                    ->rules([
                                        fn (Get $get): Closure => function (string $attribute, $value, Closure $fail) use ($get) {
                                            $maxQuantity = $get('max_quantity');
                                            if ($value > $maxQuantity) {
                                                $fail("Số lượng không thể vượt quá tồn kho ({$maxQuantity})");
                                            }
                                        },
                                    ])
                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                        $unitPrice = $get('unit_price') ?? 0;
                                        $discount = $get('discount_percentage') ?? 0;
                                        $taxRate = $get('tax_rate') ?? 0;

                                        $subtotal = $state * $unitPrice;
                                        $discountAmount = $subtotal * ($discount / 100);
                                        $taxableAmount = $subtotal - $discountAmount;
                                        $taxAmount = $taxableAmount * ($taxRate / 100);
                                        $total = $taxableAmount + $taxAmount;

                                        $set('total_price', $total);
                                    }),

                                TextInput::make('unit_price')
                                    ->label('Đơn giá')
                                    ->numeric()
                                    ->required()
                                    ->prefix('₫')
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                        $quantity = $get('quantity') ?? 0;
                                        $discount = $get('discount_percentage') ?? 0;
                                        $taxRate = $get('tax_rate') ?? 0;

                                        $subtotal = $quantity * $state;
                                        $discountAmount = $subtotal * ($discount / 100);
                                        $taxableAmount = $subtotal - $discountAmount;
                                        $taxAmount = $taxableAmount * ($taxRate / 100);
                                        $total = $taxableAmount + $taxAmount;

                                        $set('total_price', $total);
                                    }),

                                TextInput::make('discount_percentage')
                                    ->label('Giảm giá (%)')
                                    ->numeric()
                                    ->suffix('%')
                                    ->minValue(0)
                                    ->maxValue(100)
                                    ->default(0)
                                    ->reactive(),

                                TextInput::make('tax_rate')
                                    ->label('Thuế (%)')
                                    ->numeric()
                                    ->suffix('%')
                                    ->disabled()
                                    ->dehydrated(false),

                                TextInput::make('total_price')
                                    ->label('Thành tiền')
                                    ->numeric()
                                    ->prefix('₫')
                                    ->disabled()
                                    ->dehydrated(),
                            ])
                            ->columns(3)
                            ->defaultItems(1)
                            ->addActionLabel('Thêm sản phẩm')
                            ->reorderableWithButtons()
                            ->collapsible()
                            ->cloneable(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('invoice_number')
                    ->label('Số hóa đơn')
                    ->searchable()
                    ->sortable()
                    ->copyable()
                    ->weight(FontWeight::Bold),

                TextColumn::make('customer.name')
                    ->label('Khách hàng')
                    ->searchable()
                    ->sortable()
                    ->placeholder('Khách vãng lai')
                    ->limit(30),

                TextColumn::make('invoice_date')
                    ->label('Ngày xuất')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),

                TextColumn::make('total_amount')
                    ->label('Tổng tiền')
                    ->money('VND')
                    ->sortable()
                    ->weight(FontWeight::Bold)
                    ->color('success'),

                TextColumn::make('payment_method')
                    ->label('Thanh toán')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'cash' => 'success',
                        'card' => 'info',
                        'transfer' => 'warning',
                        'qr_code' => 'primary',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'cash' => 'Tiền mặt',
                        'card' => 'Thẻ',
                        'transfer' => 'Chuyển khoản',
                        'qr_code' => 'QR Code',
                        default => $state,
                    }),

                TextColumn::make('payment_status')
                    ->label('TT Thanh toán')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'paid' => 'success',
                        'pending' => 'warning',
                        'partial' => 'info',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'paid' => 'Đã thanh toán',
                        'pending' => 'Chờ thanh toán',
                        'partial' => 'Thanh toán 1 phần',
                        default => $state,
                    }),

                TextColumn::make('status')
                    ->label('Trạng thái')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'confirmed' => 'success',
                        'draft' => 'warning',
                        'cancelled' => 'danger',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'confirmed' => 'Đã xác nhận',
                        'draft' => 'Nháp',
                        'cancelled' => 'Đã hủy',
                        default => $state,
                    }),

                TextColumn::make('user.name')
                    ->label('Thu ngân')
                    ->toggleable(),
            ])
            ->defaultSort('created_at', 'desc')
            ->filters([
                SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->options([
                        'draft' => 'Nháp',
                        'confirmed' => 'Đã xác nhận',
                        'cancelled' => 'Đã hủy',
                    ]),

                SelectFilter::make('payment_status')
                    ->label('Trạng thái thanh toán')
                    ->options([
                        'pending' => 'Chờ thanh toán',
                        'paid' => 'Đã thanh toán',
                        'partial' => 'Thanh toán 1 phần',
                    ]),

                SelectFilter::make('payment_method')
                    ->label('Phương thức thanh toán')
                    ->options([
                        'cash' => 'Tiền mặt',
                        'card' => 'Thẻ',
                        'transfer' => 'Chuyển khoản',
                        'qr_code' => 'QR Code',
                    ]),

                Filter::make('created_at')
                    ->label('Ngày tạo')
                    ->form([
                        DatePicker::make('created_from')
                            ->label('Từ ngày'),
                        DatePicker::make('created_until')
                            ->label('Đến ngày'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make()
                        ->visible(fn (Invoice $record) => $record->status === 'draft'),
                    Tables\Actions\Action::make('confirm')
                        ->label('Xác nhận')
                        ->icon('heroicon-o-check-circle')
                        ->action(fn (Invoice $record) => $record->confirm())
                        ->requiresConfirmation()
                        ->visible(fn (Invoice $record) => $record->status === 'draft')
                        ->color('success'),
                    Tables\Actions\Action::make('cancel')
                        ->label('Hủy')
                        ->icon('heroicon-o-x-circle')
                        ->action(fn (Invoice $record) => $record->cancel())
                        ->requiresConfirmation()
                        ->visible(fn (Invoice $record) => $record->status === 'confirmed')
                        ->color('danger'),
                    Tables\Actions\Action::make('print_pdf')
                        ->label('In PDF')
                        ->icon('heroicon-o-printer')
                        ->url(fn (Invoice $record): string => route('invoice.pdf', $record))
                        ->openUrlInNewTab(),
                    Tables\Actions\Action::make('download_pdf')
                        ->label('Tải PDF')
                        ->icon('heroicon-o-arrow-down-tray')
                        ->url(fn (Invoice $record): string => route('invoice.download', $record)),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\ItemsRelationManager::class,
        ];
    }

    public static function getWidgets(): array
    {
        return [
            Widgets\SalesStatsWidget::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListInvoices::route('/'),
            'create' => Pages\CreateInvoice::route('/create'),
            'edit' => Pages\EditInvoice::route('/{record}/edit'),
        ];
    }
}
