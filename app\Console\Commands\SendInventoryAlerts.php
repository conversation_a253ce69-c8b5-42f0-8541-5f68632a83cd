<?php

namespace App\Console\Commands;

use App\Models\Tenant;
use App\Services\InventoryAlertService;
use Illuminate\Console\Command;

class SendInventoryAlerts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'inventory:alerts {--tenant=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send daily inventory alerts for all tenants';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $tenantId = $this->option('tenant');

        if ($tenantId) {
            $tenant = Tenant::find($tenantId);
            if (!$tenant) {
                $this->error("Tenant {$tenantId} not found");
                return 1;
            }
            $this->sendAlertsForTenant($tenant);
        } else {
            $tenants = Tenant::all();
            $this->info("Sending inventory alerts for " . $tenants->count() . " tenants...");

            foreach ($tenants as $tenant) {
                $this->sendAlertsForTenant($tenant);
            }
        }

        $this->info("Inventory alerts completed!");
        return 0;
    }

    private function sendAlertsForTenant(Tenant $tenant)
    {
        $this->info("Processing tenant: {$tenant->id}");

        $tenant->run(function () use ($tenant) {
            $alertService = app(InventoryAlertService::class);
            $alerts = $alertService->getAllAlerts();
            $summary = $alerts['summary'];

            if ($summary['critical_alerts'] > 0) {
                $this->warn("  Critical alerts found: {$summary['critical_alerts']}");
                $this->line("  - Expired: {$summary['categories']['expired']}");
                $this->line("  - Critical expiring: {$summary['categories']['critical_expiring']}");
                $this->line("  - Out of stock: {$summary['categories']['out_of_stock']}");

                // Send the alert report
                $sent = $alertService->sendDailyAlertReport();
                if ($sent) {
                    $this->info("  ✓ Alert report sent");
                } else {
                    $this->warn("  ✗ Failed to send alert report");
                }
            } else {
                $this->info("  ✓ No critical alerts");
            }
        });
    }
}
