<?php

namespace App\Services;

use App\Models\Invoice;
use App\Models\Drug;
use App\Models\Customer;
use App\Models\Batch;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class ReportService
{
    public function getDailySalesReport(\DateTime $date = null): array
    {
        $date = $date ?? now();
        $startOfDay = Carbon::parse($date)->startOfDay();
        $endOfDay = Carbon::parse($date)->endOfDay();
        
        $invoices = Invoice::confirmed()
            ->whereBetween('invoice_date', [$startOfDay, $endOfDay])
            ->with(['items.batch.drug', 'customer', 'user'])
            ->get();

        $totalRevenue = $invoices->sum('total_amount');
        $totalTax = $invoices->sum('tax_amount');
        $totalDiscount = $invoices->sum('discount_amount');

        return [
            'date' => $date->format('Y-m-d'),
            'summary' => [
                'total_invoices' => $invoices->count(),
                'total_revenue' => $totalRevenue,
                'total_tax' => $totalTax,
                'total_discount' => $totalDiscount,
                'net_revenue' => $totalRevenue - $totalTax,
                'average_invoice_value' => $invoices->count() > 0 ? $totalRevenue / $invoices->count() : 0,
            ],
            'payment_methods' => $this->getPaymentMethodBreakdown($invoices),
            'hourly_sales' => $this->getHourlySales($invoices),
            'top_selling_drugs' => $this->getTopSellingDrugs($invoices),
            'top_customers' => $this->getTopCustomers($invoices),
            'cashier_performance' => $this->getCashierPerformance($invoices),
        ];
    }

    public function getMonthlySalesReport(int $year, int $month): array
    {
        $startOfMonth = Carbon::createFromDate($year, $month, 1)->startOfMonth();
        $endOfMonth = $startOfMonth->copy()->endOfMonth();
        
        $invoices = Invoice::confirmed()
            ->whereBetween('invoice_date', [$startOfMonth, $endOfMonth])
            ->with(['items.batch.drug', 'customer', 'user'])
            ->get();

        $dailySales = $this->getDailySalesForMonth($year, $month);

        return [
            'period' => [
                'year' => $year,
                'month' => $month,
                'month_name' => $startOfMonth->format('F Y'),
            ],
            'summary' => [
                'total_invoices' => $invoices->count(),
                'total_revenue' => $invoices->sum('total_amount'),
                'total_tax' => $invoices->sum('tax_amount'),
                'total_discount' => $invoices->sum('discount_amount'),
                'average_daily_revenue' => $dailySales->avg('revenue'),
                'best_day' => $dailySales->sortByDesc('revenue')->first(),
                'worst_day' => $dailySales->sortBy('revenue')->first(),
            ],
            'daily_breakdown' => $dailySales->toArray(),
            'top_selling_drugs' => $this->getTopSellingDrugs($invoices, 20),
            'customer_analysis' => $this->getCustomerAnalysis($invoices),
            'tax_summary' => $this->getTaxSummary($invoices),
        ];
    }

    public function getInventoryReport(): array
    {
        $batches = Batch::with('drug')
            ->where('is_active', true)
            ->get();

        $expiringSoon = $batches->filter(function ($batch) {
            return $batch->expiry_date->diffInDays() <= 30 && $batch->current_quantity > 0;
        });

        $expired = $batches->filter(function ($batch) {
            return $batch->expiry_date->isPast() && $batch->current_quantity > 0;
        });

        $lowStock = $batches->filter(function ($batch) {
            return $batch->current_quantity <= 10 && $batch->current_quantity > 0;
        });

        $outOfStock = Drug::whereDoesntHave('batches', function ($query) {
            $query->where('current_quantity', '>', 0)
                ->where('expiry_date', '>', now());
        })->get();

        return [
            'summary' => [
                'total_drugs' => Drug::active()->count(),
                'total_batches' => $batches->count(),
                'total_stock_value' => $batches->sum(function ($batch) {
                    return $batch->current_quantity * $batch->import_price;
                }),
                'expiring_soon_count' => $expiringSoon->count(),
                'expired_count' => $expired->count(),
                'low_stock_count' => $lowStock->count(),
                'out_of_stock_count' => $outOfStock->count(),
            ],
            'expiring_soon' => $expiringSoon->map(function ($batch) {
                return [
                    'drug_name' => $batch->drug->name,
                    'batch_number' => $batch->batch_number,
                    'expiry_date' => $batch->expiry_date->format('d/m/Y'),
                    'days_to_expiry' => $batch->expiry_date->diffInDays(),
                    'current_quantity' => $batch->current_quantity,
                    'value' => $batch->current_quantity * $batch->import_price,
                ];
            })->sortBy('days_to_expiry')->values()->toArray(),
            'expired' => $expired->map(function ($batch) {
                return [
                    'drug_name' => $batch->drug->name,
                    'batch_number' => $batch->batch_number,
                    'expiry_date' => $batch->expiry_date->format('d/m/Y'),
                    'current_quantity' => $batch->current_quantity,
                    'value' => $batch->current_quantity * $batch->import_price,
                ];
            })->sortBy('expiry_date')->values()->toArray(),
            'low_stock' => $lowStock->map(function ($batch) {
                return [
                    'drug_name' => $batch->drug->name,
                    'batch_number' => $batch->batch_number,
                    'current_quantity' => $batch->current_quantity,
                    'reorder_level' => 10, // This could be configurable per drug
                ];
            })->sortBy('current_quantity')->values()->toArray(),
            'out_of_stock' => $outOfStock->map(function ($drug) {
                return [
                    'drug_name' => $drug->name,
                    'code' => $drug->code,
                    'last_batch_date' => $drug->batches()->latest('created_at')->first()?->created_at?->format('d/m/Y'),
                ];
            })->toArray(),
        ];
    }

    public function getCustomerReport(): array
    {
        $customers = Customer::with(['invoices' => function ($query) {
            $query->confirmed();
        }])->get();

        $topCustomers = $customers->sortByDesc('total_spent')->take(20);
        $newCustomers = $customers->where('created_at', '>=', now()->subMonth());
        $vipCustomers = $customers->where('customer_group', 'vip');

        return [
            'summary' => [
                'total_customers' => $customers->count(),
                'new_customers_this_month' => $newCustomers->count(),
                'vip_customers' => $vipCustomers->count(),
                'average_customer_value' => $customers->avg('total_spent'),
                'total_loyalty_points' => $customers->sum('loyalty_points'),
            ],
            'top_customers' => $topCustomers->map(function ($customer) {
                return [
                    'name' => $customer->name,
                    'phone' => $customer->phone,
                    'total_spent' => $customer->total_spent,
                    'loyalty_points' => $customer->loyalty_points,
                    'total_invoices' => $customer->invoices->count(),
                    'last_visit' => $customer->last_visit?->format('d/m/Y'),
                    'customer_group' => $customer->customer_group,
                ];
            })->values()->toArray(),
            'customer_groups' => $customers->groupBy('customer_group')->map(function ($group, $key) {
                return [
                    'group' => $key,
                    'count' => $group->count(),
                    'total_spent' => $group->sum('total_spent'),
                    'average_spent' => $group->avg('total_spent'),
                ];
            })->values()->toArray(),
        ];
    }

    protected function getPaymentMethodBreakdown(Collection $invoices): array
    {
        return $invoices->groupBy('payment_method')->map(function ($group, $method) {
            return [
                'method' => $method,
                'count' => $group->count(),
                'total_amount' => $group->sum('total_amount'),
                'percentage' => $group->count() / max($group->count(), 1) * 100,
            ];
        })->values()->toArray();
    }

    protected function getHourlySales(Collection $invoices): array
    {
        $hourlySales = [];
        for ($hour = 0; $hour < 24; $hour++) {
            $hourlySales[$hour] = [
                'hour' => $hour,
                'count' => 0,
                'revenue' => 0,
            ];
        }

        foreach ($invoices as $invoice) {
            $hour = $invoice->invoice_date->hour;
            $hourlySales[$hour]['count']++;
            $hourlySales[$hour]['revenue'] += $invoice->total_amount;
        }

        return array_values($hourlySales);
    }

    protected function getTopSellingDrugs(Collection $invoices, int $limit = 10): array
    {
        $drugSales = [];
        
        foreach ($invoices as $invoice) {
            foreach ($invoice->items as $item) {
                $drugId = $item->batch->drug_id;
                $drugName = $item->batch->drug->name;
                
                if (!isset($drugSales[$drugId])) {
                    $drugSales[$drugId] = [
                        'drug_name' => $drugName,
                        'quantity_sold' => 0,
                        'revenue' => 0,
                        'profit' => 0,
                    ];
                }
                
                $drugSales[$drugId]['quantity_sold'] += $item->quantity;
                $drugSales[$drugId]['revenue'] += $item->total_price;
                $drugSales[$drugId]['profit'] += ($item->unit_price - $item->batch->import_price) * $item->quantity;
            }
        }
        
        // Sort by quantity sold
        uasort($drugSales, function ($a, $b) {
            return $b['quantity_sold'] <=> $a['quantity_sold'];
        });
        
        return array_slice($drugSales, 0, $limit);
    }

    protected function getTopCustomers(Collection $invoices, int $limit = 10): array
    {
        $customerSales = [];
        
        foreach ($invoices as $invoice) {
            if ($invoice->customer) {
                $customerId = $invoice->customer_id;
                $customerName = $invoice->customer->name;
                
                if (!isset($customerSales[$customerId])) {
                    $customerSales[$customerId] = [
                        'customer_name' => $customerName,
                        'invoice_count' => 0,
                        'total_spent' => 0,
                    ];
                }
                
                $customerSales[$customerId]['invoice_count']++;
                $customerSales[$customerId]['total_spent'] += $invoice->total_amount;
            }
        }
        
        // Sort by total spent
        uasort($customerSales, function ($a, $b) {
            return $b['total_spent'] <=> $a['total_spent'];
        });
        
        return array_slice($customerSales, 0, $limit);
    }

    protected function getCashierPerformance(Collection $invoices): array
    {
        return $invoices->groupBy('user_id')->map(function ($group) {
            $user = $group->first()->user;
            return [
                'cashier_name' => $user->name,
                'invoice_count' => $group->count(),
                'total_revenue' => $group->sum('total_amount'),
                'average_invoice_value' => $group->avg('total_amount'),
            ];
        })->sortByDesc('total_revenue')->values()->toArray();
    }

    protected function getDailySalesForMonth(int $year, int $month): Collection
    {
        $startOfMonth = Carbon::createFromDate($year, $month, 1)->startOfMonth();
        $endOfMonth = $startOfMonth->copy()->endOfMonth();
        
        $dailySales = collect();
        
        for ($date = $startOfMonth->copy(); $date <= $endOfMonth; $date->addDay()) {
            $dayInvoices = Invoice::confirmed()
                ->whereDate('invoice_date', $date)
                ->get();
                
            $dailySales->push([
                'date' => $date->format('Y-m-d'),
                'day_name' => $date->format('l'),
                'invoice_count' => $dayInvoices->count(),
                'revenue' => $dayInvoices->sum('total_amount'),
                'tax' => $dayInvoices->sum('tax_amount'),
            ]);
        }
        
        return $dailySales;
    }

    protected function getCustomerAnalysis(Collection $invoices): array
    {
        $newCustomers = $invoices->filter(function ($invoice) {
            return $invoice->customer && $invoice->customer->created_at >= now()->subMonth();
        });

        $returningCustomers = $invoices->filter(function ($invoice) {
            return $invoice->customer && $invoice->customer->created_at < now()->subMonth();
        });

        return [
            'new_customers' => [
                'count' => $newCustomers->count(),
                'revenue' => $newCustomers->sum('total_amount'),
            ],
            'returning_customers' => [
                'count' => $returningCustomers->count(),
                'revenue' => $returningCustomers->sum('total_amount'),
            ],
        ];
    }

    protected function getTaxSummary(Collection $invoices): array
    {
        $prescriptionItems = collect();
        $otcItems = collect();

        foreach ($invoices as $invoice) {
            foreach ($invoice->items as $item) {
                if ($item->batch->drug->prescription_required) {
                    $prescriptionItems->push($item);
                } else {
                    $otcItems->push($item);
                }
            }
        }

        return [
            'prescription_drugs' => [
                'revenue' => $prescriptionItems->sum('total_price'),
                'tax' => $prescriptionItems->sum('tax_amount'),
                'rate' => '5%',
            ],
            'otc_drugs' => [
                'revenue' => $otcItems->sum('total_price'),
                'tax' => $otcItems->sum('tax_amount'),
                'rate' => '10%',
            ],
            'total_tax' => $invoices->sum('tax_amount'),
        ];
    }
}
