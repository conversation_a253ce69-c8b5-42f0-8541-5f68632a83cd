<?php

namespace App\Services;

use App\Contracts\SmsServiceInterface;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SmsService implements SmsServiceInterface
{
    protected string $apiUrl;
    protected string $apiKey;
    protected string $brandName;

    public function __construct()
    {
        $this->apiUrl = config('sms.api_url', 'https://api.sms-provider.com');
        $this->apiKey = config('sms.api_key', '');
        $this->brandName = config('sms.brand_name', 'PharmaSaaS');
    }

    public function send(string $phoneNumber, string $message): bool
    {
        // Validate phone number
        $phoneNumber = $this->formatPhoneNumber($phoneNumber);
        if (!$phoneNumber) {
            Log::warning('Invalid phone number format', ['phone' => $phoneNumber]);
            return false;
        }

        // For demo purposes, we'll simulate SMS sending
        if (config('app.env') === 'local' || config('sms.mock', true)) {
            return $this->mockSend($phoneNumber, $message);
        }

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->post($this->apiUrl . '/send', [
                'to' => $phoneNumber,
                'message' => $message,
                'brand_name' => $this->brandName,
            ]);

            if ($response->successful()) {
                $data = $response->json();
                Log::info('SMS sent successfully', [
                    'phone' => $phoneNumber,
                    'message_id' => $data['message_id'] ?? null,
                ]);
                return true;
            } else {
                Log::error('SMS sending failed', [
                    'phone' => $phoneNumber,
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);
                return false;
            }
        } catch (\Exception $e) {
            Log::error('SMS sending exception', [
                'phone' => $phoneNumber,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    public function sendBulk(array $recipients): array
    {
        $results = [];
        
        foreach ($recipients as $recipient) {
            $phoneNumber = $recipient['phone'] ?? '';
            $message = $recipient['message'] ?? '';
            
            $results[] = [
                'phone' => $phoneNumber,
                'success' => $this->send($phoneNumber, $message),
            ];
        }
        
        return $results;
    }

    public function getStatus(string $messageId): array
    {
        if (config('sms.mock', true)) {
            return [
                'message_id' => $messageId,
                'status' => 'delivered',
                'delivered_at' => now()->toISOString(),
            ];
        }

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
            ])->get($this->apiUrl . '/status/' . $messageId);

            if ($response->successful()) {
                return $response->json();
            }
        } catch (\Exception $e) {
            Log::error('SMS status check failed', [
                'message_id' => $messageId,
                'error' => $e->getMessage(),
            ]);
        }

        return [
            'message_id' => $messageId,
            'status' => 'unknown',
        ];
    }

    public function getBalance(): float
    {
        if (config('sms.mock', true)) {
            return 1000.0; // Mock balance
        }

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
            ])->get($this->apiUrl . '/balance');

            if ($response->successful()) {
                $data = $response->json();
                return (float) ($data['balance'] ?? 0);
            }
        } catch (\Exception $e) {
            Log::error('SMS balance check failed', ['error' => $e->getMessage()]);
        }

        return 0.0;
    }

    protected function formatPhoneNumber(string $phoneNumber): ?string
    {
        // Remove all non-digit characters
        $phoneNumber = preg_replace('/\D/', '', $phoneNumber);
        
        // Vietnamese phone number validation
        if (preg_match('/^(84|0)([3|5|7|8|9])([0-9]{8})$/', $phoneNumber, $matches)) {
            // Convert to international format
            if ($matches[1] === '0') {
                return '84' . $matches[2] . $matches[3];
            }
            return $phoneNumber;
        }
        
        return null;
    }

    protected function mockSend(string $phoneNumber, string $message): bool
    {
        // Log the SMS for debugging
        Log::info('Mock SMS sent', [
            'phone' => $phoneNumber,
            'message' => $message,
            'timestamp' => now()->toISOString(),
        ]);

        // Simulate random failures (5% failure rate)
        return rand(1, 100) > 5;
    }

    public function sendInvoiceLink(string $phoneNumber, string $invoiceNumber, string $customerPortalUrl): bool
    {
        $message = "Hóa đơn #{$invoiceNumber} đã sẵn sàng. Xem chi tiết tại: {$customerPortalUrl}";
        return $this->send($phoneNumber, $message);
    }

    public function sendPaymentReminder(string $phoneNumber, string $invoiceNumber, float $amount): bool
    {
        $formattedAmount = number_format($amount, 0, ',', '.') . 'đ';
        $message = "Nhắc nhở thanh toán: Hóa đơn #{$invoiceNumber} với số tiền {$formattedAmount} chưa được thanh toán. Vui lòng liên hệ nhà thuốc để thanh toán.";
        return $this->send($phoneNumber, $message);
    }

    public function sendPromotionMessage(string $phoneNumber, string $promotionText): bool
    {
        $message = "🎉 Khuyến mãi từ {$this->brandName}: {$promotionText}. Liên hệ nhà thuốc để biết thêm chi tiết.";
        return $this->send($phoneNumber, $message);
    }
}
