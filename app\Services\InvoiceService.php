<?php

namespace App\Services;

use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\Batch;
use App\Models\Customer;
use App\Exceptions\InsufficientStockException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Services\SmsService;

class InvoiceService
{
    public function generateInvoiceNumber(): string
    {
        $date = now()->format('Ymd');
        $sequence = Invoice::whereDate('created_at', today())->count() + 1;
        
        return "HD{$date}" . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    public function createInvoice(array $data): Invoice
    {
        DB::beginTransaction();
        
        try {
            // Create invoice
            $invoice = Invoice::create([
                'customer_id' => $data['customer_id'] ?? null,
                'user_id' => auth()->id(),
                'invoice_date' => $data['invoice_date'] ?? now(),
                'payment_method' => $data['payment_method'] ?? 'cash',
                'payment_status' => $data['payment_status'] ?? 'paid',
                'status' => 'draft',
                'notes' => $data['notes'] ?? null,
            ]);

            // Add items
            foreach ($data['items'] as $itemData) {
                $this->addItemToInvoice($invoice, $itemData);
            }

            // Calculate totals
            $invoice->calculateTotals();
            
            // Apply customer discount if applicable
            if ($invoice->customer) {
                $customerDiscount = $this->calculateCustomerDiscount($invoice);
                $invoice->update(['discount_amount' => $customerDiscount]);
                $invoice->calculateTotals();
            }

            // Auto-confirm if payment is cash
            if ($data['payment_method'] === 'cash' && ($data['auto_confirm'] ?? true)) {
                $invoice->confirm();
            }

            DB::commit();
            return $invoice->fresh(['items.batch.drug', 'customer', 'user']);
            
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    protected function addItemToInvoice(Invoice $invoice, array $itemData): InvoiceItem
    {
        $batch = Batch::findOrFail($itemData['batch_id']);
        
        // Check stock availability
        if (!$batch->canSell($itemData['quantity'])) {
            throw new InsufficientStockException(
                "Insufficient stock for {$batch->drug->name}. Available: {$batch->available_quantity}, Requested: {$itemData['quantity']}"
            );
        }

        return $invoice->items()->create([
            'batch_id' => $itemData['batch_id'],
            'quantity' => $itemData['quantity'],
            'unit_price' => $itemData['unit_price'] ?? $batch->selling_price,
            'discount_percentage' => $itemData['discount_percentage'] ?? 0,
            'tax_rate' => $batch->drug->tax_rate * 100, // Convert to percentage
        ]);
    }

    protected function calculateCustomerDiscount(Invoice $invoice): float
    {
        if (!$invoice->customer) {
            return 0;
        }

        // Simple discount calculation - can be enhanced based on customer group, loyalty level, etc.
        $discountRate = 0;
        
        // VIP customers get 5% discount
        if ($invoice->customer->customer_group === 'vip') {
            $discountRate = 0.05;
        }
        // Regular customers with high loyalty points get 2% discount
        elseif ($invoice->customer->loyalty_points >= 1000) {
            $discountRate = 0.02;
        }

        return $invoice->subtotal * $discountRate;
    }

    public function updateInvoice(Invoice $invoice, array $data): Invoice
    {
        if ($invoice->status !== 'draft') {
            throw new \Exception('Cannot update confirmed or cancelled invoice');
        }

        DB::beginTransaction();
        
        try {
            // Update invoice data
            $invoice->update([
                'customer_id' => $data['customer_id'] ?? $invoice->customer_id,
                'invoice_date' => $data['invoice_date'] ?? $invoice->invoice_date,
                'payment_method' => $data['payment_method'] ?? $invoice->payment_method,
                'payment_status' => $data['payment_status'] ?? $invoice->payment_status,
                'notes' => $data['notes'] ?? $invoice->notes,
            ]);

            // Update items if provided
            if (isset($data['items'])) {
                // Remove existing items
                $invoice->items()->delete();
                
                // Add new items
                foreach ($data['items'] as $itemData) {
                    $this->addItemToInvoice($invoice, $itemData);
                }
            }

            // Recalculate totals
            $invoice->calculateTotals();
            
            // Recalculate customer discount
            if ($invoice->customer) {
                $customerDiscount = $this->calculateCustomerDiscount($invoice);
                $invoice->update(['discount_amount' => $customerDiscount]);
                $invoice->calculateTotals();
            }

            DB::commit();
            return $invoice->fresh(['items.batch.drug', 'customer', 'user']);
            
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    public function generatePDF(Invoice $invoice): string
    {
        // Ensure invoice has all relationships loaded
        $invoice->load(['items.batch.drug', 'customer', 'user']);

        // Generate PDF
        $pdf = Pdf::loadView('invoices.pdf', compact('invoice'))
            ->setPaper('a4', 'portrait')
            ->setOptions([
                'defaultFont' => 'DejaVu Sans',
                'isHtml5ParserEnabled' => true,
                'isPhpEnabled' => true,
            ]);

        // Ensure directory exists
        $directory = storage_path('app/invoices');
        if (!file_exists($directory)) {
            mkdir($directory, 0755, true);
        }

        // Save PDF
        $filename = "invoice-{$invoice->invoice_number}.pdf";
        $path = "{$directory}/{$filename}";
        $pdf->save($path);

        return $path;
    }

    public function downloadPDF(Invoice $invoice): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        $path = $this->generatePDF($invoice);
        $filename = "Hoa-don-{$invoice->invoice_number}.pdf";

        return response()->download($path, $filename, [
            'Content-Type' => 'application/pdf',
        ]);
    }

    public function streamPDF(Invoice $invoice): \Illuminate\Http\Response
    {
        $invoice->load(['items.batch.drug', 'customer', 'user']);

        $pdf = Pdf::loadView('invoices.pdf', compact('invoice'))
            ->setPaper('a4', 'portrait')
            ->setOptions([
                'defaultFont' => 'DejaVu Sans',
                'isHtml5ParserEnabled' => true,
                'isPhpEnabled' => true,
            ]);

        return $pdf->stream("Hoa-don-{$invoice->invoice_number}.pdf");
    }

    public function sendToCustomerPortal(Invoice $invoice): bool
    {
        if (!$invoice->customer || !$invoice->customer->phone) {
            return false;
        }

        $smsService = app(SmsService::class);
        return $smsService->sendInvoiceLink(
            $invoice->customer->phone,
            $invoice->invoice_number,
            $invoice->customer_portal_url
        );
    }

    public function sendPaymentReminder(Invoice $invoice): bool
    {
        if (!$invoice->customer || !$invoice->customer->phone) {
            return false;
        }

        if ($invoice->payment_status === 'paid') {
            return false; // Already paid
        }

        $smsService = app(SmsService::class);
        return $smsService->sendPaymentReminder(
            $invoice->customer->phone,
            $invoice->invoice_number,
            $invoice->total_amount
        );
    }

    public function sendToTaxAuthority(Invoice $invoice): array
    {
        // Mock implementation - replace with actual tax authority API
        $data = [
            'invoice_number' => $invoice->invoice_number,
            'tax_code' => tenant()->tax_code ?? 'DEMO_TAX_CODE',
            'customer_name' => $invoice->customer?->name ?? 'Walk-in Customer',
            'customer_tax_code' => $invoice->customer?->tax_code,
            'total_amount' => $invoice->total_amount,
            'tax_amount' => $invoice->tax_amount,
            'items' => $invoice->items->map(function ($item) {
                return [
                    'name' => $item->batch->drug->name,
                    'quantity' => $item->quantity,
                    'unit_price' => $item->unit_price,
                    'tax_rate' => $item->tax_rate,
                ];
            })->toArray(),
        ];

        // Simulate API call
        $response = [
            'status' => 'success',
            'transaction_id' => 'TCT' . Str::random(10),
            'lookup_code' => Str::random(8),
        ];

        $invoice->update([
            'tax_authority_status' => 'sent',
            'tax_transaction_id' => $response['transaction_id'],
        ]);

        return $response;
    }

    public function getDailySalesReport(\DateTime $date = null): array
    {
        $date = $date ?? now();
        
        $invoices = Invoice::confirmed()
            ->whereDate('invoice_date', $date)
            ->with(['items.batch.drug', 'customer'])
            ->get();

        return [
            'date' => $date->format('Y-m-d'),
            'total_invoices' => $invoices->count(),
            'total_revenue' => $invoices->sum('total_amount'),
            'total_tax' => $invoices->sum('tax_amount'),
            'payment_methods' => $invoices->groupBy('payment_method')->map->count(),
            'top_selling_drugs' => $this->getTopSellingDrugs($invoices),
        ];
    }

    protected function getTopSellingDrugs($invoices, int $limit = 10): array
    {
        $drugSales = [];
        
        foreach ($invoices as $invoice) {
            foreach ($invoice->items as $item) {
                $drugId = $item->batch->drug_id;
                $drugName = $item->batch->drug->name;
                
                if (!isset($drugSales[$drugId])) {
                    $drugSales[$drugId] = [
                        'name' => $drugName,
                        'quantity' => 0,
                        'revenue' => 0,
                    ];
                }
                
                $drugSales[$drugId]['quantity'] += $item->quantity;
                $drugSales[$drugId]['revenue'] += $item->total_price;
            }
        }
        
        // Sort by quantity sold
        uasort($drugSales, function ($a, $b) {
            return $b['quantity'] <=> $a['quantity'];
        });
        
        return array_slice($drugSales, 0, $limit);
    }
}
