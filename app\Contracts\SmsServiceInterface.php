<?php

namespace App\Contracts;

interface SmsServiceInterface
{
    /**
     * Send SMS message
     */
    public function send(string $phoneNumber, string $message): bool;

    /**
     * Send bulk SMS messages
     */
    public function sendBulk(array $recipients): array;

    /**
     * Get SMS delivery status
     */
    public function getStatus(string $messageId): array;

    /**
     * Get account balance
     */
    public function getBalance(): float;
}
