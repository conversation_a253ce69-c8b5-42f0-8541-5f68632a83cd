<?php

namespace App\Services;

use App\Models\Batch;
use App\Models\Drug;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class InventoryAlertService
{
    protected int $lowStockThreshold = 10;
    protected int $expiryWarningDays = 30;
    protected int $criticalExpiryDays = 7;

    public function __construct()
    {
        $this->lowStockThreshold = config('inventory.low_stock_threshold', 10);
        $this->expiryWarningDays = config('inventory.expiry_warning_days', 30);
        $this->criticalExpiryDays = config('inventory.critical_expiry_days', 7);
    }

    public function getAllAlerts(): array
    {
        return [
            'expired' => $this->getExpiredBatches(),
            'expiring_critical' => $this->getCriticalExpiringBatches(),
            'expiring_soon' => $this->getExpiringSoonBatches(),
            'low_stock' => $this->getLowStockBatches(),
            'out_of_stock' => $this->getOutOfStockDrugs(),
            'summary' => $this->getAlertSummary(),
        ];
    }

    public function getExpiredBatches(): Collection
    {
        return Batch::with('drug')
            ->where('is_active', true)
            ->where('current_quantity', '>', 0)
            ->where('expiry_date', '<', now())
            ->orderBy('expiry_date', 'asc')
            ->get()
            ->map(function ($batch) {
                return [
                    'id' => $batch->id,
                    'drug_name' => $batch->drug->name,
                    'batch_number' => $batch->batch_number,
                    'expiry_date' => $batch->expiry_date,
                    'current_quantity' => $batch->current_quantity,
                    'value' => $batch->current_quantity * $batch->import_price,
                    'days_expired' => now()->diffInDays($batch->expiry_date),
                    'severity' => 'critical',
                    'action_required' => 'Loại bỏ khỏi kho',
                ];
            });
    }

    public function getCriticalExpiringBatches(): Collection
    {
        return Batch::with('drug')
            ->where('is_active', true)
            ->where('current_quantity', '>', 0)
            ->whereBetween('expiry_date', [now(), now()->addDays($this->criticalExpiryDays)])
            ->orderBy('expiry_date', 'asc')
            ->get()
            ->map(function ($batch) {
                return [
                    'id' => $batch->id,
                    'drug_name' => $batch->drug->name,
                    'batch_number' => $batch->batch_number,
                    'expiry_date' => $batch->expiry_date,
                    'current_quantity' => $batch->current_quantity,
                    'value' => $batch->current_quantity * $batch->import_price,
                    'days_to_expiry' => now()->diffInDays($batch->expiry_date),
                    'severity' => 'high',
                    'action_required' => 'Bán ưu tiên hoặc khuyến mãi',
                ];
            });
    }

    public function getExpiringSoonBatches(): Collection
    {
        return Batch::with('drug')
            ->where('is_active', true)
            ->where('current_quantity', '>', 0)
            ->whereBetween('expiry_date', [
                now()->addDays($this->criticalExpiryDays + 1),
                now()->addDays($this->expiryWarningDays)
            ])
            ->orderBy('expiry_date', 'asc')
            ->get()
            ->map(function ($batch) {
                return [
                    'id' => $batch->id,
                    'drug_name' => $batch->drug->name,
                    'batch_number' => $batch->batch_number,
                    'expiry_date' => $batch->expiry_date,
                    'current_quantity' => $batch->current_quantity,
                    'value' => $batch->current_quantity * $batch->import_price,
                    'days_to_expiry' => now()->diffInDays($batch->expiry_date),
                    'severity' => 'medium',
                    'action_required' => 'Theo dõi và lên kế hoạch bán',
                ];
            });
    }

    public function getLowStockBatches(): Collection
    {
        return Batch::with('drug')
            ->where('is_active', true)
            ->where('current_quantity', '>', 0)
            ->where('current_quantity', '<=', $this->lowStockThreshold)
            ->where('expiry_date', '>', now())
            ->orderBy('current_quantity', 'asc')
            ->get()
            ->map(function ($batch) {
                return [
                    'id' => $batch->id,
                    'drug_name' => $batch->drug->name,
                    'batch_number' => $batch->batch_number,
                    'current_quantity' => $batch->current_quantity,
                    'threshold' => $this->lowStockThreshold,
                    'expiry_date' => $batch->expiry_date,
                    'severity' => $batch->current_quantity <= 3 ? 'high' : 'medium',
                    'action_required' => 'Nhập hàng bổ sung',
                ];
            });
    }

    public function getOutOfStockDrugs(): Collection
    {
        return Drug::whereDoesntHave('batches', function ($query) {
            $query->where('current_quantity', '>', 0)
                ->where('expiry_date', '>', now())
                ->where('is_active', true);
        })
        ->where('is_active', true)
        ->get()
        ->map(function ($drug) {
            $lastBatch = $drug->batches()->latest('created_at')->first();
            return [
                'id' => $drug->id,
                'drug_name' => $drug->name,
                'code' => $drug->code,
                'last_batch_date' => $lastBatch?->created_at,
                'last_batch_number' => $lastBatch?->batch_number,
                'severity' => 'high',
                'action_required' => 'Nhập hàng ngay',
            ];
        });
    }

    public function getAlertSummary(): array
    {
        $expired = $this->getExpiredBatches();
        $criticalExpiring = $this->getCriticalExpiringBatches();
        $expiringSoon = $this->getExpiringSoonBatches();
        $lowStock = $this->getLowStockBatches();
        $outOfStock = $this->getOutOfStockDrugs();

        return [
            'total_alerts' => $expired->count() + $criticalExpiring->count() + $expiringSoon->count() + $lowStock->count() + $outOfStock->count(),
            'critical_alerts' => $expired->count() + $criticalExpiring->count() + $outOfStock->count(),
            'expired_value' => $expired->sum('value'),
            'expiring_value' => $criticalExpiring->sum('value') + $expiringSoon->sum('value'),
            'categories' => [
                'expired' => $expired->count(),
                'critical_expiring' => $criticalExpiring->count(),
                'expiring_soon' => $expiringSoon->count(),
                'low_stock' => $lowStock->count(),
                'out_of_stock' => $outOfStock->count(),
            ],
        ];
    }

    public function getAlertsForDashboard(): array
    {
        // Cache alerts for 5 minutes to improve performance
        return Cache::remember('inventory_alerts_dashboard', 300, function () {
            $summary = $this->getAlertSummary();
            $criticalAlerts = collect();

            // Get top 5 most critical alerts
            $expired = $this->getExpiredBatches()->take(2);
            $criticalExpiring = $this->getCriticalExpiringBatches()->take(2);
            $outOfStock = $this->getOutOfStockDrugs()->take(1);

            $criticalAlerts = $criticalAlerts->merge($expired)
                ->merge($criticalExpiring)
                ->merge($outOfStock)
                ->take(5);

            return [
                'summary' => $summary,
                'critical_alerts' => $criticalAlerts->toArray(),
            ];
        });
    }

    public function markBatchAsHandled(int $batchId, string $action, string $notes = null): bool
    {
        try {
            $batch = Batch::findOrFail($batchId);
            
            switch ($action) {
                case 'dispose':
                    $batch->update([
                        'current_quantity' => 0,
                        'notes' => ($batch->notes ?? '') . "\n[" . now()->format('d/m/Y H:i') . "] Đã loại bỏ: " . ($notes ?? 'Hết hạn'),
                    ]);
                    break;
                    
                case 'return_supplier':
                    $batch->update([
                        'current_quantity' => 0,
                        'notes' => ($batch->notes ?? '') . "\n[" . now()->format('d/m/Y H:i') . "] Trả nhà cung cấp: " . ($notes ?? 'Hết hạn'),
                    ]);
                    break;
                    
                case 'discount_sale':
                    $batch->update([
                        'notes' => ($batch->notes ?? '') . "\n[" . now()->format('d/m/Y H:i') . "] Bán khuyến mãi: " . ($notes ?? 'Sắp hết hạn'),
                    ]);
                    break;
                    
                case 'reorder':
                    // This would typically create a purchase order
                    Log::info("Reorder requested for drug: {$batch->drug->name}", [
                        'batch_id' => $batchId,
                        'notes' => $notes,
                    ]);
                    break;
            }
            
            return true;
        } catch (\Exception $e) {
            Log::error("Failed to mark batch as handled", [
                'batch_id' => $batchId,
                'action' => $action,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    public function sendDailyAlertReport(): bool
    {
        try {
            $alerts = $this->getAllAlerts();
            $summary = $alerts['summary'];
            
            // Only send if there are critical alerts
            if ($summary['critical_alerts'] > 0) {
                // This would send email/SMS to pharmacy manager
                Log::info("Daily inventory alert report", [
                    'total_alerts' => $summary['total_alerts'],
                    'critical_alerts' => $summary['critical_alerts'],
                    'expired_value' => $summary['expired_value'],
                ]);
                
                // TODO: Implement actual notification sending
                // Mail::to($pharmacyManager)->send(new DailyInventoryAlert($alerts));
                
                return true;
            }
            
            return false;
        } catch (\Exception $e) {
            Log::error("Failed to send daily alert report", ['error' => $e->getMessage()]);
            return false;
        }
    }

    public function getRecommendedActions(): array
    {
        $alerts = $this->getAllAlerts();
        $recommendations = [];

        // Expired items
        if ($alerts['expired']->count() > 0) {
            $recommendations[] = [
                'priority' => 'critical',
                'title' => 'Loại bỏ thuốc hết hạn',
                'description' => "Có {$alerts['expired']->count()} lô thuốc đã hết hạn cần loại bỏ khỏi kho",
                'action' => 'dispose_expired',
                'items' => $alerts['expired']->take(5)->toArray(),
            ];
        }

        // Critical expiring items
        if ($alerts['expiring_critical']->count() > 0) {
            $recommendations[] = [
                'priority' => 'high',
                'title' => 'Bán ưu tiên thuốc sắp hết hạn',
                'description' => "Có {$alerts['expiring_critical']->count()} lô thuốc sẽ hết hạn trong {$this->criticalExpiryDays} ngày tới",
                'action' => 'priority_sale',
                'items' => $alerts['expiring_critical']->take(5)->toArray(),
            ];
        }

        // Out of stock items
        if ($alerts['out_of_stock']->count() > 0) {
            $recommendations[] = [
                'priority' => 'high',
                'title' => 'Nhập hàng bổ sung',
                'description' => "Có {$alerts['out_of_stock']->count()} loại thuốc đã hết hàng",
                'action' => 'reorder',
                'items' => $alerts['out_of_stock']->take(5)->toArray(),
            ];
        }

        return $recommendations;
    }
}
