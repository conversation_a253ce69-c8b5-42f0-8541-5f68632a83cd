<?php

namespace App\Filament\Pages;

use App\Services\ReportService;
use Filament\Pages\Page;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Carbon\Carbon;

class SalesReport extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';
    protected static ?string $navigationGroup = 'Reports';
    protected static ?string $navigationLabel = 'Báo cáo bán hàng';
    protected static ?string $title = 'Báo cáo bán hàng';

    protected static string $view = 'filament.pages.sales-report';

    public ?string $reportType = 'daily';
    public ?string $selectedDate = null;
    public ?int $selectedMonth = null;
    public ?int $selectedYear = null;
    public array $reportData = [];

    public function mount(): void
    {
        $this->selectedDate = now()->format('Y-m-d');
        $this->selectedMonth = now()->month;
        $this->selectedYear = now()->year;
        $this->generateReport();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('reportType')
                    ->label('Loại báo cáo')
                    ->options([
                        'daily' => 'Báo cáo ngày',
                        'monthly' => 'Báo cáo tháng',
                    ])
                    ->default('daily')
                    ->reactive()
                    ->afterStateUpdated(fn () => $this->generateReport()),

                DatePicker::make('selectedDate')
                    ->label('Chọn ngày')
                    ->default(now())
                    ->visible(fn ($get) => $get('reportType') === 'daily')
                    ->reactive()
                    ->afterStateUpdated(fn () => $this->generateReport()),

                Select::make('selectedMonth')
                    ->label('Tháng')
                    ->options([
                        1 => 'Tháng 1', 2 => 'Tháng 2', 3 => 'Tháng 3',
                        4 => 'Tháng 4', 5 => 'Tháng 5', 6 => 'Tháng 6',
                        7 => 'Tháng 7', 8 => 'Tháng 8', 9 => 'Tháng 9',
                        10 => 'Tháng 10', 11 => 'Tháng 11', 12 => 'Tháng 12',
                    ])
                    ->default(now()->month)
                    ->visible(fn ($get) => $get('reportType') === 'monthly')
                    ->reactive()
                    ->afterStateUpdated(fn () => $this->generateReport()),

                Select::make('selectedYear')
                    ->label('Năm')
                    ->options(collect(range(now()->year - 5, now()->year + 1))->mapWithKeys(fn ($year) => [$year => $year]))
                    ->default(now()->year)
                    ->visible(fn ($get) => $get('reportType') === 'monthly')
                    ->reactive()
                    ->afterStateUpdated(fn () => $this->generateReport()),
            ])
            ->columns(4);
    }

    public function generateReport(): void
    {
        $reportService = app(ReportService::class);

        if ($this->reportType === 'daily') {
            $date = Carbon::parse($this->selectedDate);
            $this->reportData = $reportService->getDailySalesReport($date);
        } else {
            $this->reportData = $reportService->getMonthlySalesReport($this->selectedYear, $this->selectedMonth);
        }
    }

    public function getReportData(): array
    {
        return $this->reportData;
    }
}
