<?php

namespace App\Filament\Widgets;

use App\Services\InventoryAlertService;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class InventoryAlertsWidget extends BaseWidget
{
    protected static ?int $sort = 2;
    protected static ?string $pollingInterval = '30s';

    protected function getStats(): array
    {
        $alertService = app(InventoryAlertService::class);
        $alerts = $alertService->getAlertsForDashboard();
        $summary = $alerts['summary'];

        return [
            Stat::make('Thuốc hết hạn', $summary['categories']['expired'])
                ->description('Cần loại bỏ ngay')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color($summary['categories']['expired'] > 0 ? 'danger' : 'success')
                ->chart($this->getExpiredChart()),

            Stat::make('Sắp hết hạn', $summary['categories']['critical_expiring'])
                ->description('Trong 7 ngày tới')
                ->descriptionIcon('heroicon-m-clock')
                ->color($summary['categories']['critical_expiring'] > 0 ? 'warning' : 'success'),

            Stat::make('Sắp hết hàng', $summary['categories']['low_stock'])
                ->description('Dưới ngưỡng tồn kho')
                ->descriptionIcon('heroicon-m-archive-box-x-mark')
                ->color($summary['categories']['low_stock'] > 0 ? 'warning' : 'success'),

            Stat::make('Hết hàng', $summary['categories']['out_of_stock'])
                ->description('Cần nhập hàng ngay')
                ->descriptionIcon('heroicon-m-x-circle')
                ->color($summary['categories']['out_of_stock'] > 0 ? 'danger' : 'success'),
        ];
    }

    protected function getExpiredChart(): array
    {
        // Simple chart showing expired items over last 7 days
        $data = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i);
            // This is a simplified chart - in real implementation, you'd query actual data
            $data[] = rand(0, 5);
        }
        return $data;
    }
}
